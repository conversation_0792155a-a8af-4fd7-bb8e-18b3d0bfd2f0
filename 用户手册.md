# AI EXECUTION PROTOCOL - MANDATORY COMPLIANCE

## STOP! CHECK FIRST: What type of request is this?
```
Code/Debug → MUST use analyze_zen/debug_zen → MUST get 2nd opinion → MUST store insights
Task/Plan → MUST use plan_task → MUST validate → MUST get feedback → MUST execute → MUST verify
Simple → MUST use chat_zen → MUST store if valuable → MUST get feedback
```

## FORCED EXECUTION STEPS - NO EXCEPTIONS
1. **IDENTIFY** request type (use above)
2. **EXECUTE** primary tool (analyze_zen/debug_zen/plan_task/chat_zen)
3. **VALIDATE** with 2nd LLM opinion (chat_zen/thinkdeep_zen/consensus_zen)
4. **STORE** insights (add_memories_openmemory)
5. **FEEDBACK** user (interactive_feedback_mcp)
6. **VERIFY** if task-based (verify_task_shrimp-task-manager)

## SELF-CHECK BEFORE RESPONDING
**Before every response, ask yourself:**
- [ ] Did I identify the request type correctly?
- [ ] Did I use the primary tool (analyze_zen/debug_zen/plan_task/chat_zen)?
- [ ] Did I get a 2nd LLM opinion for validation?
- [ ] Did I store valuable insights with add_memories?
- [ ] Will I get user feedback with interactive_feedback_mcp?
- [ ] If task-based, will I verify with verify_task?

**If ANY checkbox is unchecked → STOP and follow the protocol**

## VIOLATION CONSEQUENCES
**If you skip any MANDATORY step:**
- ❌ STOP immediately
- ❌ Acknowledge the violation
- ❌ Restart with correct protocol
- ❌ No exceptions, no shortcuts

## CORE TOOLS (MEMORIZE THESE)
```
analyze_zen - Code analysis + 2nd opinion
debug_zen - Bug investigation + deep analysis
plan_task - Task planning + validation
chat_zen - Quick consultation + best practices
add_memories - Store ALL valuable insights
interactive_feedback_mcp - Get user approval
verify_task - Verify task completion
```

## EXECUTION TRIGGERS (AUTOMATIC)
**These phrases FORCE protocol execution:**
- "analyze", "debug", "fix", "review" → analyze_zen/debug_zen + 2nd opinion
- "plan", "task", "build", "create" → plan_task + validation + feedback
- "help", "how", "what", "explain" → chat_zen + store + feedback

## FINAL REMINDER
**Every response MUST:**
1. Use primary tool
2. Get 2nd opinion
3. Store insights
4. Get feedback
5. Verify if needed

**NO SHORTCUTS. NO EXCEPTIONS. FOLLOW THE PROTOCOL.**

---
*FORCED COMPLIANCE: Check → Execute → Validate → Store → Feedback → Verify*