# AI Workflow Rules

## Core Execution Protocol

### MANDATORY: Tool Selection Priority
1. **Complex Analysis** → Use Zen tools (analyze_zen, debug_zen, thinkdeep_zen)
2. **Task Planning** → Use shrimp-task-manager
3. **Context/Memory** → Use OpenMemory tools
4. **User Feedback** → ALWAYS use interactive_feedback_mcp before major actions

### MANDATORY: Workflow Stages

#### Stage 1: Analysis (REQUIRED for complex requests)
```
IF request involves code analysis/debugging/architecture:
  → Use analyze_zen OR debug_zen OR thinkdeep_zen
  → Gather context with codebase-retrieval
  → Store insights with add_memories_openmemory
```

#### Stage 2: Planning (REQUIRED for multi-step tasks)
```
IF request needs task breakdown:
  → Use plan_task_shrimp-task-manager
  → Check memories with hybrid_search_openmemory
  → Validate plan with interactive_feedback_mcp
```

#### Stage 3: Execution (REQUIRED)
```
ALWAYS:
  → Execute tasks via execute_task_shrimp-task-manager
  → Verify with verify_task_shrimp-task-manager
  → Get user feedback with interactive_feedback_mcp
```

### FORBIDDEN Actions
- Skip user feedback for major changes
- Execute code changes without analysis
- Ignore task verification
- Proceed without context gathering

## Quick Decision Tree

### Request Type Detection
```
Code Analysis/Debug → analyze_zen/debug_zen → Store findings → Get feedback
Task Planning → plan_task → Check memories → Validate → Execute
Simple Query → chat_zen → Store if valuable → Get feedback
Complex Problem → thinkdeep_zen → Plan → Execute → Verify
```

### Tool Integration Map
```
shrimp-task-manager: plan_task → split_tasks → execute_task → verify_task
OpenMemory: hybrid_search → add_memories → get_entity_relations
Zen: analyze_zen → debug_zen → thinkdeep_zen → chat_zen
Feedback: interactive_feedback_mcp (MANDATORY before major actions)
```

### Execution Checklist
- [ ] Understand request complexity
- [ ] Choose appropriate analysis tool
- [ ] Gather context/memories
- [ ] Create plan if multi-step
- [ ] Execute with verification
- [ ] Get user feedback
- [ ] Store valuable insights

## Common Scenarios

### Code Analysis Request
```
"Analyze this code/project" → analyze_zen → add_memories → interactive_feedback
```

### Bug/Issue Report
```
"Fix this bug" → debug_zen → plan_task → execute_task → verify_task → feedback
```

### Feature Development
```
"Build feature X" → plan_task → hybrid_search → split_tasks → execute → verify → feedback
```

### Architecture Review
```
"Review architecture" → thinkdeep_zen → add_memories → consensus_zen → feedback
```

## Behavioral Constraints

### MUST DO
- Use interactive_feedback_mcp before major actions
- Store valuable insights with add_memories_openmemory
- Verify tasks with verify_task_shrimp-task-manager
- Follow the decision tree for tool selection

### MUST NOT DO
- Skip user feedback for code changes
- Execute without proper analysis
- Ignore context gathering
- Proceed without verification

### Quality Standards
- Chinese comments for all code
- Specific, measurable task descriptions
- Clear dependency relationships
- Complete verification before completion

---

*Simplified AI Workflow Rules - Follow the decision tree, use feedback, verify everything.*