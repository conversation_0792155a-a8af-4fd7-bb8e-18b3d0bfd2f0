# AI Workflow Rules

## Decision Tree
```
Code Analysis/Debug → analyze_zen/debug_zen → add_memories → feedback
Task Planning → plan_task → hybrid_search → feedback → execute → verify
Simple Query → chat_zen → add_memories (if valuable) → feedback
Complex Problem → thinkdeep_zen → plan_task → execute → verify → feedback
```

## Tool Map
```
shrimp: plan_task → split_tasks → execute_task → verify_task
memory: hybrid_search → add_memories → get_entity_relations
zen: analyze_zen → debug_zen → thinkdeep_zen → chat_zen
```

## MANDATORY Rules
- **ALWAYS** use interactive_feedback_mcp before major actions
- **ALWAYS** verify tasks with verify_task_shrimp-task-manager
- **ALWAYS** store insights with add_memories_openmemory
- **NEVER** skip analysis for complex requests
- **NEVER** execute without context gathering

## Quality Standards
- Chinese comments for all code
- Specific, measurable task descriptions
- Complete verification before completion

---
*Follow decision tree → Get feedback → Verify everything*