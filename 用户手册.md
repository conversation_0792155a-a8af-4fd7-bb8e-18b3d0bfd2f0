# AI Workflow Rules

## Decision Tree
```
Code Analysis → analyze_zen/codereview_zen → codebase-retrieval → add_memories → feedback
Debug Issues → debug_zen → codebase-retrieval → web-search → add_memories → feedback
Task Planning → plan_task → hybrid_search → feedback → execute → verify
Architecture → thinkdeep_zen/consensus_zen → render-mermaid → add_memories → feedback
Refactoring → refactor_zen → codebase-retrieval → add_memories → feedback
Security → secaudit_zen → codebase-retrieval → web-search → add_memories → feedback
Testing → testgen_zen → codebase-retrieval → add_memories → feedback
Documentation → docgen_zen → codebase-retrieval → add_memories → feedback
Pre-commit → precommit_zen → codebase-retrieval → add_memories → feedback
Simple Query → chat_zen → web-search → add_memories → feedback
```

## Complete Tool Map
```
# Task Management
shrimp: plan_task → split_tasks → execute_task → verify_task → delete_task

# Memory & Context
memory: hybrid_search → add_memories → get_entity_relations → get_intelligent_recommendations

# Analysis & Development
zen: analyze_zen → debug_zen → thinkdeep_zen → chat_zen → codereview_zen →
     refactor_zen → secaudit_zen → testgen_zen → docgen_zen → precommit_zen →
     planner_zen → consensus_zen → tracer_zen → challenge_zen

# Code & Files
augment: codebase-retrieval → str-replace-editor → save-file → view → remove-files

# Web & Research
web: web-search → web-fetch

# Process & System
process: launch-process → read-process → write-process → kill-process → list-processes

# Feedback & Memory
feedback: interactive_feedback_mcp → remember → get_system_info_mcp

# Documentation & Visualization
docs: render-mermaid → Context_7 (resolve-library-id → get-library-docs)
```

## MANDATORY Rules
- **ALWAYS** use interactive_feedback_mcp before major actions
- **ALWAYS** verify with verify_task_shrimp-task-manager
- **ALWAYS** store insights with add_memories_openmemory
- **ALWAYS** use codebase-retrieval before code changes
- **NEVER** skip analysis for complex requests
- **NEVER** execute without context gathering

## Quality Standards
- Chinese comments for all code
- Use codebase-retrieval + web-search for context
- Complete verification before completion

---
*Follow decision tree → Use all tools → Get feedback → Verify everything*